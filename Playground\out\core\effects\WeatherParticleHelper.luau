-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Workspace = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Workspace
local WeatherSystem
do
	WeatherSystem = setmetatable({}, {
		__tostring = function()
			return "WeatherSystem"
		end,
	})
	WeatherSystem.__index = WeatherSystem
	function WeatherSystem.new(...)
		local self = setmetatable({}, WeatherSystem)
		return self:constructor(...) or self
	end
	function WeatherSystem:constructor()
	end
	function WeatherSystem:createRealisticRain(intensity, windSpeed)
		if windSpeed == nil then
			windSpeed = 0
		end
		print(`🌧️ Creating realistic rain with intensity {intensity}`)
		-- Clear any existing rain
		self:clearWeatherEffect("rain")
		local emitters = {}
		local parts = {}
		-- Create a large rain area that follows the player
		local rainArea = Instance.new("Part")
		rainArea.Name = "RainArea"
		rainArea.Size = Vector3.new(1000, 20, 1000)
		rainArea.Position = Vector3.new(0, 350, 0)
		rainArea.Anchored = true
		rainArea.CanCollide = false
		rainArea.Transparency = 1
		rainArea.Parent = Workspace
		table.insert(parts, rainArea)
		-- Create the main rain emitter
		local rainEmitter = Instance.new("ParticleEmitter")
		rainEmitter.Name = "RealisticRainEmitter"
		-- REALISTIC RAIN CONFIGURATION - Based on research and best practices
		local config = {
			emissionRate = intensity * 600 + 200,
			lifetime = NumberRange.new(3, 6),
			speed = NumberRange.new(40, 70),
			acceleration = Vector3.new(windSpeed * 2, -196, windSpeed),
			size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.08), NumberSequenceKeypoint.new(0.2, 0.12), NumberSequenceKeypoint.new(1, 0.04) }),
			transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.1), NumberSequenceKeypoint.new(0.8, 0.2), NumberSequenceKeypoint.new(1, 1.0) }),
			color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(220, 240, 255)), ColorSequenceKeypoint.new(1, Color3.fromRGB(180, 200, 230)) }),
			texture = "rbxasset://textures/particles/sparkles_main.dds",
			shape = Enum.ParticleEmitterShape.Box,
			shapeInOut = Enum.ParticleEmitterShapeInOut.Outward,
			shapeStyle = Enum.ParticleEmitterShapeStyle.Volume,
			windAffectsDrag = true,
			drag = 0.1,
			lightEmission = 0.05,
			lightInfluence = 0.9,
			orientation = Enum.ParticleOrientation.VelocityParallel,
			rate = intensity * 600 + 200,
			rotSpeed = NumberRange.new(0, 0),
			rotation = NumberRange.new(0, 0),
			spreadAngle = Vector2.new(5, 5),
			velocityInheritance = 0,
			zOffset = 0,
		}
		self:applyParticleConfig(rainEmitter, config)
		rainEmitter.Parent = rainArea
		table.insert(emitters, rainEmitter)
		-- Create additional emitters for good coverage without performance issues
		for i = 0, 3 do
			local additionalRainPart = Instance.new("Part")
			additionalRainPart.Name = `RainZone_{i}`
			additionalRainPart.Size = Vector3.new(400, 10, 400)
			additionalRainPart.Position = Vector3.new(math.random(-200, 200), 250 + math.random(-30, 30), math.random(-200, 200))
			additionalRainPart.Anchored = true
			additionalRainPart.CanCollide = false
			additionalRainPart.Transparency = 1
			additionalRainPart.Parent = Workspace
			table.insert(parts, additionalRainPart)
			local additionalEmitter = Instance.new("ParticleEmitter")
			additionalEmitter.Name = `RainEmitter_{i}`
			self:applyParticleConfig(additionalEmitter, config)
			additionalEmitter.Parent = additionalRainPart
			table.insert(emitters, additionalEmitter)
		end
		-- Store references
		self.activeEmitters.rain = emitters
		self.weatherParts.rain = parts
		-- Start rain ground impact effects
		self:startRainGroundEffects(intensity)
		print(`✅ Created {#emitters} rain emitters`)
		return emitters
	end
	function WeatherSystem:startRainGroundEffects(intensity)
		-- Create small splash effects when rain hits the ground
		local splashLoop
		splashLoop = function()
			if not (self.activeEmitters.rain ~= nil) then
				return nil
			end
			-- Create random splash effects
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < math.floor(intensity * 5)) then
						break
					end
					local splashPosition = Vector3.new(math.random(-250, 250), 0.5, math.random(-250, 250))
					-- Create small splash particle
					local splashPart = Instance.new("Part")
					splashPart.Name = "RainSplash"
					splashPart.Size = Vector3.new(0.1, 0.1, 0.1)
					splashPart.Position = splashPosition
					splashPart.Anchored = true
					splashPart.CanCollide = false
					splashPart.Transparency = 1
					splashPart.Parent = Workspace
					local splashEmitter = Instance.new("ParticleEmitter")
					splashEmitter.Rate = 200
					splashEmitter.Lifetime = NumberRange.new(0.5, 1.2)
					splashEmitter.Speed = NumberRange.new(5, 12)
					splashEmitter.Acceleration = Vector3.new(0, -15, 0)
					splashEmitter.Size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.2), NumberSequenceKeypoint.new(0.5, 0.3), NumberSequenceKeypoint.new(1, 0.1) })
					splashEmitter.Transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.1), NumberSequenceKeypoint.new(0.7, 0.4), NumberSequenceKeypoint.new(1, 1) })
					splashEmitter.Color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(240, 250, 255)), ColorSequenceKeypoint.new(1, Color3.fromRGB(180, 210, 240)) })
					splashEmitter.Texture = "rbxasset://textures/particles/sparkles_main.dds"
					splashEmitter.SpreadAngle = Vector2.new(45, 45)
					splashEmitter.Parent = splashPart
					-- Enable emitter briefly then clean up
					splashEmitter.Enabled = true
					task.delay(0.1, function()
						splashEmitter.Enabled = false
						task.delay(1, function()
							return splashPart:Destroy()
						end)
					end)
				end
			end
			task.wait(0.3)
			splashLoop()
		end
		-- Start the splash loop without storing connection since task.spawn returns thread, not RBXScriptConnection
		task.spawn(function()
			return splashLoop()
		end)
	end
	function WeatherSystem:createSnowEffect(intensity, windSpeed)
		if windSpeed == nil then
			windSpeed = 0
		end
		print(`❄️ Creating REALISTIC snow with intensity {intensity} and wind {windSpeed}`)
		-- Clear any existing snow
		self:clearWeatherEffect("snow")
		local emitters = {}
		local parts = {}
		local numZones = math.max(2, math.floor(intensity * 4))
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numZones) then
					break
				end
				local snowPart = Instance.new("Part")
				snowPart.Name = `SnowZone_{i}`
				snowPart.Size = Vector3.new(300, 5, 300)
				snowPart.Position = Vector3.new(math.random(-200, 200), 250 + math.random(-20, 20), math.random(-200, 200))
				snowPart.Anchored = true
				snowPart.CanCollide = false
				snowPart.Transparency = 1
				snowPart.Parent = Workspace
				table.insert(parts, snowPart)
				local snowEmitter = Instance.new("ParticleEmitter")
				snowEmitter.Name = "SnowEmitter"
				-- REALISTIC SNOW CONFIGURATION - Based on real snow physics
				local config = {
					emissionRate = intensity * 150 + 50,
					lifetime = NumberRange.new(8, 15),
					speed = NumberRange.new(1, 5),
					acceleration = Vector3.new(windSpeed * 4, -10 - intensity * 5, windSpeed * 3),
					size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.15), NumberSequenceKeypoint.new(0.5, 0.25 + intensity * 0.15), NumberSequenceKeypoint.new(1, 0.08) }),
					transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.0), NumberSequenceKeypoint.new(0.8, 0.1), NumberSequenceKeypoint.new(1, 0.8) }),
					color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 255)), ColorSequenceKeypoint.new(1, Color3.fromRGB(245, 250, 255)) }),
					texture = "rbxasset://textures/particles/sparkles_main.dds",
					shape = Enum.ParticleEmitterShape.Box,
					shapeInOut = Enum.ParticleEmitterShapeInOut.Outward,
					shapeStyle = Enum.ParticleEmitterShapeStyle.Volume,
					windAffectsDrag = true,
					drag = 0.5,
					lightEmission = 0.2,
					lightInfluence = 0.8,
					orientation = Enum.ParticleOrientation.FacingCamera,
					rate = intensity * 150 + 50,
					rotSpeed = NumberRange.new(-50, 50),
					rotation = NumberRange.new(0, 360),
					spreadAngle = Vector2.new(20, 20),
					velocityInheritance = 0,
					zOffset = 0,
				}
				self:applyParticleConfig(snowEmitter, config)
				snowEmitter.Parent = snowPart
				table.insert(emitters, snowEmitter)
			end
		end
		self.activeEmitters.snow = emitters
		self.weatherParts.snow = parts
		return emitters
	end
	function WeatherSystem:createStormEffect(intensity)
		local emitters = {}
		local parts = {}
		local numZones = math.max(6, math.floor(intensity * 12))
		local windSpeed = intensity * 15 + 10
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numZones) then
					break
				end
				local stormPart = Instance.new("Part")
				stormPart.Name = `StormZone_{i}`
				stormPart.Size = Vector3.new(150, 1, 150)
				stormPart.Position = Vector3.new(math.random(-400, 400), 220 + math.random(-30, 30), math.random(-400, 400))
				stormPart.Anchored = true
				stormPart.CanCollide = false
				stormPart.Transparency = 1
				stormPart.Parent = Workspace
				table.insert(parts, stormPart)
				local stormEmitter = Instance.new("ParticleEmitter")
				stormEmitter.Name = "StormEmitter"
				local config = {
					emissionRate = intensity * 200 + 100,
					lifetime = NumberRange.new(1.5, 3),
					speed = NumberRange.new(40 + intensity * 40, 80 + intensity * 60),
					acceleration = Vector3.new(windSpeed, -80 - intensity * 30, windSpeed * 0.5),
					size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.15), NumberSequenceKeypoint.new(0.3, 0.25), NumberSequenceKeypoint.new(1, 0.05) }),
					transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.2), NumberSequenceKeypoint.new(0.6, 0.3), NumberSequenceKeypoint.new(1, 1) }),
					color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(180, 200, 230)), ColorSequenceKeypoint.new(1, Color3.fromRGB(120, 140, 180)) }),
					texture = "rbxasset://textures/particles/sparkles_main.dds",
					shape = Enum.ParticleEmitterShape.Box,
					shapeInOut = Enum.ParticleEmitterShapeInOut.Outward,
					shapeStyle = Enum.ParticleEmitterShapeStyle.Volume,
					windAffectsDrag = true,
					drag = 0.05,
					lightEmission = 0,
					lightInfluence = 1,
					orientation = Enum.ParticleOrientation.VelocityParallel,
					rate = intensity * 200 + 100,
					rotSpeed = NumberRange.new(0, 0),
					rotation = NumberRange.new(0, 0),
					spreadAngle = Vector2.new(8, 8),
					velocityInheritance = 0,
					zOffset = 0,
				}
				self:applyParticleConfig(stormEmitter, config)
				stormEmitter.Parent = stormPart
				table.insert(emitters, stormEmitter)
			end
		end
		self.activeEmitters.storm = emitters
		self.weatherParts.storm = parts
		return emitters
	end
	function WeatherSystem:createThunderstormEffect(intensity)
		local emitters = {}
		local parts = {}
		local numZones = math.max(8, math.floor(intensity * 15))
		local windSpeed = intensity * 20 + 15
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numZones) then
					break
				end
				local thunderPart = Instance.new("Part")
				thunderPart.Name = `ThunderstormZone_{i}`
				thunderPart.Size = Vector3.new(200, 1, 200)
				thunderPart.Position = Vector3.new(math.random(-500, 500), 250 + math.random(-40, 40), math.random(-500, 500))
				thunderPart.Anchored = true
				thunderPart.CanCollide = false
				thunderPart.Transparency = 1
				thunderPart.Parent = Workspace
				table.insert(parts, thunderPart)
				local thunderEmitter = Instance.new("ParticleEmitter")
				thunderEmitter.Name = "ThunderstormEmitter"
				local config = {
					emissionRate = intensity * 250 + 150,
					lifetime = NumberRange.new(1, 2.5),
					speed = NumberRange.new(60 + intensity * 50, 100 + intensity * 80),
					acceleration = Vector3.new(windSpeed * 1.2, -100 - intensity * 40, windSpeed * 0.8),
					size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.2), NumberSequenceKeypoint.new(0.2, 0.3), NumberSequenceKeypoint.new(1, 0.05) }),
					transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.1), NumberSequenceKeypoint.new(0.5, 0.2), NumberSequenceKeypoint.new(1, 1) }),
					color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(160, 180, 210)), ColorSequenceKeypoint.new(1, Color3.fromRGB(100, 120, 160)) }),
					texture = "rbxasset://textures/particles/sparkles_main.dds",
					shape = Enum.ParticleEmitterShape.Box,
					shapeInOut = Enum.ParticleEmitterShapeInOut.Outward,
					shapeStyle = Enum.ParticleEmitterShapeStyle.Volume,
					windAffectsDrag = true,
					drag = 0.02,
					lightEmission = 0.1,
					lightInfluence = 1,
					orientation = Enum.ParticleOrientation.VelocityParallel,
					rate = intensity * 250 + 150,
					rotSpeed = NumberRange.new(0, 0),
					rotation = NumberRange.new(0, 0),
					spreadAngle = Vector2.new(10, 10),
					velocityInheritance = 0,
					zOffset = 0,
				}
				self:applyParticleConfig(thunderEmitter, config)
				thunderEmitter.Parent = thunderPart
				table.insert(emitters, thunderEmitter)
			end
		end
		self.activeEmitters.thunderstorm = emitters
		self.weatherParts.thunderstorm = parts
		return emitters
	end
	function WeatherSystem:createFogEffect(intensity)
		local emitters = {}
		local parts = {}
		local numZones = math.max(5, math.floor(intensity * 10))
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numZones) then
					break
				end
				local fogPart = Instance.new("Part")
				fogPart.Name = `FogZone_{i}`
				fogPart.Size = Vector3.new(80, 1, 80)
				fogPart.Position = Vector3.new(math.random(-200, 200), 20 + math.random(-10, 30), math.random(-200, 200))
				fogPart.Anchored = true
				fogPart.CanCollide = false
				fogPart.Transparency = 1
				fogPart.Parent = Workspace
				table.insert(parts, fogPart)
				local fogEmitter = Instance.new("ParticleEmitter")
				fogEmitter.Name = "FogEmitter"
				local config = {
					emissionRate = intensity * 40 + 20,
					lifetime = NumberRange.new(8, 15),
					speed = NumberRange.new(1, 3 + intensity * 2),
					acceleration = Vector3.new(0, 2 + intensity, 0),
					size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 2 + intensity * 3), NumberSequenceKeypoint.new(0.5, 4 + intensity * 5), NumberSequenceKeypoint.new(1, 6 + intensity * 7) }),
					transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.8), NumberSequenceKeypoint.new(0.3, 0.6 - intensity * 0.2), NumberSequenceKeypoint.new(1, 1) }),
					color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(220, 220, 230)), ColorSequenceKeypoint.new(1, Color3.fromRGB(200, 200, 210)) }),
					texture = "rbxasset://textures/particles/sparkles_main.dds",
					shape = Enum.ParticleEmitterShape.Box,
					shapeInOut = Enum.ParticleEmitterShapeInOut.Outward,
					shapeStyle = Enum.ParticleEmitterShapeStyle.Volume,
					windAffectsDrag = true,
					drag = 0.8,
					lightEmission = 0,
					lightInfluence = 0.3,
					orientation = Enum.ParticleOrientation.FacingCamera,
					rate = intensity * 40 + 20,
					rotSpeed = NumberRange.new(0, 0),
					rotation = NumberRange.new(0, 0),
					spreadAngle = Vector2.new(15, 15),
					velocityInheritance = 0,
					zOffset = 0,
				}
				self:applyParticleConfig(fogEmitter, config)
				fogEmitter.Parent = fogPart
				table.insert(emitters, fogEmitter)
			end
		end
		self.activeEmitters.fog = emitters
		self.weatherParts.fog = parts
		return emitters
	end
	function WeatherSystem:createSandstormEffect(intensity)
		local emitters = {}
		local parts = {}
		local numZones = math.max(6, math.floor(intensity * 10))
		local windSpeed = intensity * 25 + 20
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numZones) then
					break
				end
				local sandPart = Instance.new("Part")
				sandPart.Name = `SandstormZone_{i}`
				sandPart.Size = Vector3.new(120, 1, 120)
				sandPart.Position = Vector3.new(math.random(-300, 300), 50 + math.random(-20, 50), math.random(-300, 300))
				sandPart.Anchored = true
				sandPart.CanCollide = false
				sandPart.Transparency = 1
				sandPart.Parent = Workspace
				table.insert(parts, sandPart)
				local sandEmitter = Instance.new("ParticleEmitter")
				sandEmitter.Name = "SandstormEmitter"
				local config = {
					emissionRate = intensity * 120 + 80,
					lifetime = NumberRange.new(3, 6),
					speed = NumberRange.new(15 + intensity * 20, 30 + intensity * 35),
					acceleration = Vector3.new(windSpeed, -2 - intensity * 3, windSpeed * 0.6),
					size = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.3), NumberSequenceKeypoint.new(0.5, 0.5 + intensity * 0.4), NumberSequenceKeypoint.new(1, 0.2) }),
					transparency = NumberSequence.new({ NumberSequenceKeypoint.new(0, 0.4), NumberSequenceKeypoint.new(0.6, 0.5 - intensity * 0.1), NumberSequenceKeypoint.new(1, 1) }),
					color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(200, 170, 120)), ColorSequenceKeypoint.new(1, Color3.fromRGB(180, 150, 100)) }),
					texture = "rbxasset://textures/particles/sparkles_main.dds",
					shape = Enum.ParticleEmitterShape.Box,
					shapeInOut = Enum.ParticleEmitterShapeInOut.Outward,
					shapeStyle = Enum.ParticleEmitterShapeStyle.Volume,
					windAffectsDrag = true,
					drag = 0.2,
					lightEmission = 0,
					lightInfluence = 0.9,
					orientation = Enum.ParticleOrientation.FacingCamera,
					rate = intensity * 120 + 80,
					rotSpeed = NumberRange.new(0, 0),
					rotation = NumberRange.new(0, 0),
					spreadAngle = Vector2.new(20, 20),
					velocityInheritance = 0,
					zOffset = 0,
				}
				self:applyParticleConfig(sandEmitter, config)
				sandEmitter.Parent = sandPart
				table.insert(emitters, sandEmitter)
			end
		end
		self.activeEmitters.sandstorm = emitters
		self.weatherParts.sandstorm = parts
		return emitters
	end
	function WeatherSystem:applyParticleConfig(emitter, config)
		emitter.Rate = config.rate
		emitter.Lifetime = config.lifetime
		emitter.Speed = config.speed
		emitter.Acceleration = config.acceleration
		emitter.Size = config.size
		emitter.Transparency = config.transparency
		emitter.Color = config.color
		emitter.Texture = config.texture
		emitter.Shape = config.shape
		emitter.ShapeInOut = config.shapeInOut
		emitter.ShapeStyle = config.shapeStyle
		emitter.WindAffectsDrag = config.windAffectsDrag
		emitter.Drag = config.drag
		emitter.LightEmission = config.lightEmission
		emitter.LightInfluence = config.lightInfluence
		emitter.Orientation = config.orientation
		emitter.RotSpeed = config.rotSpeed
		emitter.Rotation = config.rotation
		emitter.SpreadAngle = config.spreadAngle
		emitter.VelocityInheritance = config.velocityInheritance
		emitter.ZOffset = config.zOffset
		emitter.Enabled = true
	end
	function WeatherSystem:clearWeatherEffect(weatherType)
		print(`🧹 Clearing {weatherType} weather effects`)
		-- Clear emitters
		local _activeEmitters = self.activeEmitters
		local _weatherType = weatherType
		local emitters = _activeEmitters[_weatherType]
		if emitters then
			-- ▼ ReadonlyArray.forEach ▼
			local _callback = function(emitter)
				if emitter.Parent then
					emitter.Enabled = false
					task.delay(3, function()
						if emitter.Parent then
							emitter:Destroy()
						end
					end)
				end
			end
			for _k, _v in emitters do
				_callback(_v, _k - 1, emitters)
			end
			-- ▲ ReadonlyArray.forEach ▲
			local _activeEmitters_1 = self.activeEmitters
			local _weatherType_1 = weatherType
			_activeEmitters_1[_weatherType_1] = nil
		end
		-- Clear parts
		local _weatherParts = self.weatherParts
		local _weatherType_1 = weatherType
		local parts = _weatherParts[_weatherType_1]
		if parts then
			-- ▼ ReadonlyArray.forEach ▼
			local _callback = function(part)
				task.delay(3, function()
					if part.Parent then
						part:Destroy()
					end
				end)
			end
			for _k, _v in parts do
				_callback(_v, _k - 1, parts)
			end
			-- ▲ ReadonlyArray.forEach ▲
			local _weatherParts_1 = self.weatherParts
			local _weatherType_2 = weatherType
			_weatherParts_1[_weatherType_2] = nil
		end
		-- Clear rain connections
		if weatherType == "rain" then
			local _exp = self.rainConnections
			-- ▼ ReadonlyArray.forEach ▼
			local _callback = function(connection)
				if connection.Connected then
					connection:Disconnect()
				end
			end
			for _k, _v in _exp do
				_callback(_v, _k - 1, _exp)
			end
			-- ▲ ReadonlyArray.forEach ▲
			self.rainConnections = {}
		end
		print(`✅ Cleared {weatherType} weather effects`)
	end
	function WeatherSystem:clearAllWeatherEffects()
		-- Clear all emitters
		local _exp = self.activeEmitters
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(emitters)
			-- ▼ ReadonlyArray.forEach ▼
			local _callback_1 = function(emitter)
				if emitter.Parent then
					emitter.Enabled = false
					task.delay(2, function()
						return emitter:Destroy()
					end)
				end
			end
			for _k, _v in emitters do
				_callback_1(_v, _k - 1, emitters)
			end
			-- ▲ ReadonlyArray.forEach ▲
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.activeEmitters)
		-- Clear all parts
		local _exp_1 = self.weatherParts
		-- ▼ ReadonlyMap.forEach ▼
		local _callback_1 = function(parts)
			-- ▼ ReadonlyArray.forEach ▼
			local _callback_2 = function(part)
				task.delay(2, function()
					return part:Destroy()
				end)
			end
			for _k, _v in parts do
				_callback_2(_v, _k - 1, parts)
			end
			-- ▲ ReadonlyArray.forEach ▲
		end
		for _k, _v in _exp_1 do
			_callback_1(_v, _k, _exp_1)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.weatherParts)
		print("🧹 Cleared all weather effects")
	end
	function WeatherSystem:updateWeatherIntensity(weatherType, newIntensity)
		local _activeEmitters = self.activeEmitters
		local _weatherType = weatherType
		local emitters = _activeEmitters[_weatherType]
		if not emitters then
			print(`⚠️ No active {weatherType} effects to update`)
			return nil
		end
		-- Update emission rate based on weather type and intensity
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(emitter)
			repeat
				if weatherType == "rain" then
					emitter.Rate = newIntensity * 150 + 50
					break
				end
				if weatherType == "snow" then
					emitter.Rate = newIntensity * 80 + 30
					break
				end
				if weatherType == "storm" then
					emitter.Rate = newIntensity * 200 + 100
					break
				end
				if weatherType == "thunderstorm" then
					emitter.Rate = newIntensity * 250 + 150
					break
				end
				if weatherType == "fog" then
					emitter.Rate = newIntensity * 40 + 20
					break
				end
				if weatherType == "sandstorm" then
					emitter.Rate = newIntensity * 120 + 80
					break
				end
			until true
		end
		for _k, _v in emitters do
			_callback(_v, _k - 1, emitters)
		end
		-- ▲ ReadonlyArray.forEach ▲
		print(`🌦️ Updated {weatherType} intensity to {newIntensity}`)
	end
	function WeatherSystem:getActiveWeatherTypes()
		local types = {}
		local _exp = self.activeEmitters
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(_, key)
			local _key = key
			table.insert(types, _key)
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return types
	end
	function WeatherSystem:isWeatherActive(weatherType)
		local _activeEmitters = self.activeEmitters
		local _weatherType = weatherType
		return _activeEmitters[_weatherType] ~= nil
	end
	function WeatherSystem:getEmitterCount(weatherType)
		local _activeEmitters = self.activeEmitters
		local _weatherType = weatherType
		local emitters = _activeEmitters[_weatherType]
		return if emitters then #emitters else 0
	end
	function WeatherSystem:createWeatherEffect(weatherType, intensity, windSpeed)
		if windSpeed == nil then
			windSpeed = 0
		end
		print(`🌦️ Creating {weatherType} weather with intensity {intensity}`)
		-- Clear existing effects of this type first
		self:clearWeatherEffect(weatherType)
		repeat
			local _fallthrough = false
			if weatherType == "rain" then
				_fallthrough = true
			end
			if _fallthrough or weatherType == "heavy_rain" then
				return self:createRealisticRain(intensity, windSpeed)
			end
			if weatherType == "snow" then
				_fallthrough = true
			end
			if _fallthrough or weatherType == "blizzard" then
				return self:createSnowEffect(intensity, windSpeed)
			end
			if weatherType == "storm" then
				return self:createStormEffect(intensity)
			end
			if weatherType == "thunderstorm" then
				return self:createThunderstormEffect(intensity)
			end
			if weatherType == "fog" then
				return self:createFogEffect(intensity)
			end
			if weatherType == "sandstorm" then
				return self:createSandstormEffect(intensity)
			end
			print(`⚠️ Unknown weather type: {weatherType}`)
			return {}
		until true
	end
	WeatherSystem.activeEmitters = {}
	WeatherSystem.weatherParts = {}
	WeatherSystem.rainConnections = {}
end
-- Legacy alias for backward compatibility
local WeatherParticleHelper = WeatherSystem
return {
	WeatherSystem = WeatherSystem,
	WeatherParticleHelper = WeatherParticleHelper,
}
