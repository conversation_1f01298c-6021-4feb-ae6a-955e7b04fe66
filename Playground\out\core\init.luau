-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Core Framework - Enterprise-grade architecture
local _CoreFramework = TS.import(script, game:GetService("ReplicatedStorage"), "core", "CoreFramework")
exports.CoreFramework = _CoreFramework.CoreFramework
exports.initializeCoreFramework = _CoreFramework.initializeCoreFramework
exports.shutdownCoreFramework = _CoreFramework.shutdownCoreFramework
exports.Core = _CoreFramework.Core
-- Foundation - Base types and services
exports.Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
exports.BrandedTypes = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").BrandedTypes
exports.createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
exports.BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
exports.ServiceContainer = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "ServiceContainer").ServiceContainer
exports.ServiceLifecycle = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "enums", "ServiceLifecycle").ServiceLifecycle
-- Networking - Modern networking with validation
exports.NetworkService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "NetworkService").NetworkService
exports.NetworkValidationService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "NetworkValidationService").NetworkValidationService
exports.NetworkError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "errors", "NetworkError").NetworkError
-- State Management - Centralized state with middleware support
exports.StateManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "state", "StateManager").StateManager
exports.StateError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "state", "errors", "StateError").StateError
-- Design System - Centralized styling
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
exports.COLORS = _design.COLORS
exports.SIZES = _design.SIZES
exports.TYPOGRAPHY = _design.TYPOGRAPHY
exports.BORDER_RADIUS = _design.BORDER_RADIUS
-- GUI Components - Modern React components
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "grid", "Grid") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "input", "Input") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "modal", "Modal") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "list", "ListView") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "image", "Image") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "AutoDockFrame") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "useZIndex") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "actionbar") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "slider") or {} do
	exports[_k] = _v
end
-- Effects System - Visual and audio effects
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "FrameAnimationHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "ParticleHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "VisualEffectUtils") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "TrailHelper") or {} do
	exports[_k] = _v
end
-- Animation System - Character and object animations
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "AnimationBuilder") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "CharacterJointManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "LimbAnimator") or {} do
	exports[_k] = _v
end
-- Utilities
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper") or {} do
	exports[_k] = _v
end
-- Character System
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "character", "CharacterBuilder") or {} do
	exports[_k] = _v
end
-- Data Persistence - Modern data management
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "DataStoreHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "PlayerDataManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "DataStore") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "RemoteEventTypes") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "GlobalData") or {} do
	exports[_k] = _v
end
-- World Systems - Environmental interactions
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world") or {} do
	exports[_k] = _v
end
-- Entity Management - Game object management
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "interfaces", "Entity") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "enums", "EntityType") or {} do
	exports[_k] = _v
end
-- AI System - Behavior-based AI
exports.AIController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "AIController").AIController
exports.AIState = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "enums", "AIState").AIState
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors") or {} do
	exports[_k] = _v
end
-- Debug System - Development tools
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug") or {} do
	exports[_k] = _v
end
-- Client-side Core Framework
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "client") or {} do
	exports[_k] = _v
end
return exports
