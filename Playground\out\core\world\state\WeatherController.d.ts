import { WeatherOptions, WeatherType } from "./interfaces/WeatherOptions";
/**
 * WeatherController - Advanced weather system with realistic effects
 * Provides weather effects with proper particles, sounds, and transitions
 */
export declare class WeatherController {
    private static instance;
    private currentWeather;
    private currentIntensity;
    private isTransitioning;
    private constructor();
    static getInstance(): WeatherController;
    /**
     * Set weather with optional smooth transition
     */
    setWeather(options: WeatherOptions): void;
    /**
     * Apply weather change immediately without transition
     */
    private applyWeatherImmediate;
    private setClearWeather;
    private setRainWeather;
    private setSnowWeather;
    private setStormWeather;
    private setThunderstormWeather;
    private setFogWeather;
    private setSandstormWeather;
    /**
     * Get current weather type
     */
    getCurrentWeather(): WeatherType;
    /**
     * Get current weather intensity
     */
    getCurrentIntensity(): number;
    /**
     * Clear weather effects
     */
    private clearWeatherEffects;
    /**
     * Cleanup all weather effects
     */
    cleanup(): void;
}
