import { Workspace, RunService } from "@rbxts/services";

export interface WeatherConfig {
    emissionRate: number;
    lifetime: NumberRange;
    speed: NumberRange;
    acceleration: Vector3;
    size: NumberSequence;
    transparency: NumberSequence;
    color: ColorSequence;
    texture: string;
    shape: Enum.ParticleEmitterShape;
    shapeInOut: Enum.ParticleEmitterShapeInOut;
    shapeStyle: Enum.ParticleEmitterShapeStyle;
    windAffectsDrag: boolean;
    drag: number;
    lightEmission: number;
    lightInfluence: number;
    orientation: Enum.ParticleOrientation;
    rate: number;
    rotSpeed: NumberRange;
    rotation: NumberRange;
    spreadAngle: Vector2;
    velocityInheritance: number;
    zOffset: number;
}

export class WeatherSystem {
    private static activeEmitters: Map<string, ParticleEmitter[]> = new Map();
    private static weatherParts: Map<string, Part[]> = new Map();
    private static rainConnections: RBXScriptConnection[] = [];

    /**
     * Create REALISTIC rain using proper Roblox particle techniques
     */
    static createRealisticRain(intensity: number, windSpeed = 0): ParticleEmitter[] {
        print(`🌧️ Creating realistic rain with intensity ${intensity}`);

        // Clear any existing rain
        this.clearWeatherEffect("rain");

        const emitters: ParticleEmitter[] = [];
        const parts: Part[] = [];

        // Create a large rain area that follows the player
        const rainArea = new Instance("Part");
        rainArea.Name = "RainArea";
        rainArea.Size = new Vector3(1000, 20, 1000); // HUGE area for massive rain coverage
        rainArea.Position = new Vector3(0, 350, 0); // Higher in the sky
        rainArea.Anchored = true;
        rainArea.CanCollide = false;
        rainArea.Transparency = 1;
        rainArea.Parent = Workspace;
        parts.push(rainArea);

        // Create the main rain emitter
        const rainEmitter = new Instance("ParticleEmitter");
        rainEmitter.Name = "RealisticRainEmitter";

        // REALISTIC RAIN CONFIGURATION - Based on research and best practices
        const config: WeatherConfig = {
            emissionRate: intensity * 600 + 200, // Optimized particle count
            lifetime: new NumberRange(3, 6), // Realistic fall time from sky to ground
            speed: new NumberRange(40, 70), // Fast falling speed like real rain
            acceleration: new Vector3(windSpeed * 2, -196, windSpeed), // Realistic gravity (9.8m/s² * 20 studs/meter)
            size: new NumberSequence([
                new NumberSequenceKeypoint(0, 0.08), // Small rain drop start
                new NumberSequenceKeypoint(0.2, 0.12), // Slight growth for visibility
                new NumberSequenceKeypoint(1, 0.04) // Shrink as it falls
            ]),
            transparency: new NumberSequence([
                new NumberSequenceKeypoint(0, 0.1), // Slightly transparent for realism
                new NumberSequenceKeypoint(0.8, 0.2), // Stay mostly visible
                new NumberSequenceKeypoint(1, 1.0) // Fade out at end
            ]),
            color: new ColorSequence([
                new ColorSequenceKeypoint(0, Color3.fromRGB(220, 240, 255)), // Light blue-white like real rain
                new ColorSequenceKeypoint(1, Color3.fromRGB(180, 200, 230)) // Darker blue
            ]),
            texture: "rbxasset://textures/particles/sparkles_main.dds", // BEST texture for rain drops
            shape: Enum.ParticleEmitterShape.Box,
            shapeInOut: Enum.ParticleEmitterShapeInOut.Outward,
            shapeStyle: Enum.ParticleEmitterShapeStyle.Volume,
            windAffectsDrag: true,
            drag: 0.1, // Slight air resistance for realism
            lightEmission: 0.05, // Minimal glow - rain doesn't glow much
            lightInfluence: 0.9, // Affected by environmental lighting
            orientation: Enum.ParticleOrientation.VelocityParallel, // CRITICAL: Rain streaks aligned with velocity
            rate: intensity * 600 + 200, // Match emission rate
            rotSpeed: new NumberRange(0, 0), // No rotation for rain
            rotation: new NumberRange(0, 0),
            spreadAngle: new Vector2(5, 5), // Natural, focused rain spread
            velocityInheritance: 0,
            zOffset: 0
        };

        this.applyParticleConfig(rainEmitter, config);
        rainEmitter.Parent = rainArea;
        emitters.push(rainEmitter);

        // Create additional emitters for good coverage without performance issues
        for (let i = 0; i < 4; i++) {
            const additionalRainPart = new Instance("Part");
            additionalRainPart.Name = `RainZone_${i}`;
            additionalRainPart.Size = new Vector3(400, 10, 400); // Optimized additional zones
            additionalRainPart.Position = new Vector3(
                math.random(-200, 200),
                250 + math.random(-30, 30),
                math.random(-200, 200)
            );
            additionalRainPart.Anchored = true;
            additionalRainPart.CanCollide = false;
            additionalRainPart.Transparency = 1;
            additionalRainPart.Parent = Workspace;
            parts.push(additionalRainPart);

            const additionalEmitter = new Instance("ParticleEmitter");
            additionalEmitter.Name = `RainEmitter_${i}`;
            this.applyParticleConfig(additionalEmitter, config);
            additionalEmitter.Parent = additionalRainPart;
            emitters.push(additionalEmitter);
        }

        // Store references
        this.activeEmitters.set("rain", emitters);
        this.weatherParts.set("rain", parts);

        // Start rain ground impact effects
        this.startRainGroundEffects(intensity);

        print(`✅ Created ${emitters.size()} rain emitters`);
        return emitters;
    }

    /**
     * Create ground impact effects for rain
     */
    private static startRainGroundEffects(intensity: number): void {
        // Create small splash effects when rain hits the ground
        const splashLoop = () => {
            if (!this.activeEmitters.has("rain")) {
                return; // Stop if rain is no longer active
            }

            // Create random splash effects
            for (let i = 0; i < math.floor(intensity * 5); i++) {
                const splashPosition = new Vector3(
                    math.random(-250, 250),
                    0.5, // Just above ground
                    math.random(-250, 250)
                );

                // Create small splash particle
                const splashPart = new Instance("Part");
                splashPart.Name = "RainSplash";
                splashPart.Size = new Vector3(0.1, 0.1, 0.1);
                splashPart.Position = splashPosition;
                splashPart.Anchored = true;
                splashPart.CanCollide = false;
                splashPart.Transparency = 1;
                splashPart.Parent = Workspace;

                const splashEmitter = new Instance("ParticleEmitter");
                splashEmitter.Rate = 200; // MANY more splash particles
                splashEmitter.Lifetime = new NumberRange(0.5, 1.2); // Much longer lasting splash
                splashEmitter.Speed = new NumberRange(5, 12); // Much faster splash spread
                splashEmitter.Acceleration = new Vector3(0, -15, 0); // Stronger gravity
                splashEmitter.Size = new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.2), // Much larger splash particles
                    new NumberSequenceKeypoint(0.5, 0.3),
                    new NumberSequenceKeypoint(1, 0.1) // Still visible at end
                ]);
                splashEmitter.Transparency = new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.1), // More opaque
                    new NumberSequenceKeypoint(0.7, 0.4),
                    new NumberSequenceKeypoint(1, 1)
                ]);
                splashEmitter.Color = new ColorSequence([
                    new ColorSequenceKeypoint(0, Color3.fromRGB(240, 250, 255)), // Brighter splash
                    new ColorSequenceKeypoint(1, Color3.fromRGB(180, 210, 240))
                ]);
                splashEmitter.Texture = "rbxasset://textures/particles/sparkles_main.dds";
                splashEmitter.SpreadAngle = new Vector2(45, 45); // Wide splash spread
                splashEmitter.Parent = splashPart;

                // Enable emitter briefly then clean up
                splashEmitter.Enabled = true;
                task.delay(0.1, () => {
                    splashEmitter.Enabled = false;
                    task.delay(1, () => splashPart.Destroy());
                });
            }

            task.wait(0.3);
            splashLoop();
        };

        // Start the splash loop without storing connection since task.spawn returns thread, not RBXScriptConnection
        task.spawn(() => splashLoop());
    }

    /**
     * Create REALISTIC snow particle effects using proper techniques
     */
    static createSnowEffect(intensity: number, windSpeed = 0): ParticleEmitter[] {
        print(`❄️ Creating REALISTIC snow with intensity ${intensity} and wind ${windSpeed}`);

        // Clear any existing snow
        this.clearWeatherEffect("snow");

        const emitters: ParticleEmitter[] = [];
        const parts: Part[] = [];

        const numZones = math.max(2, math.floor(intensity * 4)); // Fewer zones for better performance
        
        for (let i = 0; i < numZones; i++) {
            const snowPart = new Instance("Part");
            snowPart.Name = `SnowZone_${i}`;
            snowPart.Size = new Vector3(300, 5, 300); // Larger snow zones
            snowPart.Position = new Vector3(
                math.random(-200, 200),
                250 + math.random(-20, 20), // Higher in sky
                math.random(-200, 200)
            );
            snowPart.Anchored = true;
            snowPart.CanCollide = false;
            snowPart.Transparency = 1;
            snowPart.Parent = Workspace;
            parts.push(snowPart);

            const snowEmitter = new Instance("ParticleEmitter");
            snowEmitter.Name = "SnowEmitter";
            
            // REALISTIC SNOW CONFIGURATION - Based on real snow physics
            const config: WeatherConfig = {
                emissionRate: intensity * 150 + 50, // More snow particles for visibility
                lifetime: new NumberRange(8, 15), // Snow falls slowly, longer lifetime
                speed: new NumberRange(1, 5), // Very slow initial speed like real snow
                acceleration: new Vector3(windSpeed * 4, -10 - intensity * 5, windSpeed * 3), // Gentle falling with wind
                size: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.15), // Medium snowflake start
                    new NumberSequenceKeypoint(0.5, 0.25 + intensity * 0.15), // Grow slightly for visibility
                    new NumberSequenceKeypoint(1, 0.08) // Shrink as it melts/lands
                ]),
                transparency: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.0), // Fully opaque snow
                    new NumberSequenceKeypoint(0.8, 0.1), // Stay mostly opaque
                    new NumberSequenceKeypoint(1, 0.8) // Fade at end
                ]),
                color: new ColorSequence([
                    new ColorSequenceKeypoint(0, Color3.fromRGB(255, 255, 255)), // Pure white snow
                    new ColorSequenceKeypoint(1, Color3.fromRGB(245, 250, 255)) // Slightly blue-white
                ]),
                texture: "rbxasset://textures/particles/sparkles_main.dds", // Good texture for snowflakes
                shape: Enum.ParticleEmitterShape.Box,
                shapeInOut: Enum.ParticleEmitterShapeInOut.Outward,
                shapeStyle: Enum.ParticleEmitterShapeStyle.Volume,
                windAffectsDrag: true,
                drag: 0.5, // High drag - snow is light and affected by air
                lightEmission: 0.2, // Snow reflects light slightly
                lightInfluence: 0.8, // Affected by environmental lighting
                orientation: Enum.ParticleOrientation.FacingCamera, // Snow faces camera for best visibility
                rate: intensity * 150 + 50, // Match emission rate
                rotSpeed: new NumberRange(-50, 50), // Snow tumbles as it falls
                rotation: new NumberRange(0, 360), // Random starting rotation
                spreadAngle: new Vector2(20, 20), // Wide spread for natural snow drift
                velocityInheritance: 0,
                zOffset: 0
            };

            this.applyParticleConfig(snowEmitter, config);
            snowEmitter.Parent = snowPart;
            emitters.push(snowEmitter);
        }

        this.activeEmitters.set("snow", emitters);
        this.weatherParts.set("snow", parts);
        
        return emitters;
    }

    /**
     * Create storm effects with heavy rain and wind
     */
    static createStormEffect(intensity: number): ParticleEmitter[] {
        const emitters: ParticleEmitter[] = [];
        const parts: Part[] = [];
        
        const numZones = math.max(6, math.floor(intensity * 12));
        const windSpeed = intensity * 15 + 10;
        
        for (let i = 0; i < numZones; i++) {
            const stormPart = new Instance("Part");
            stormPart.Name = `StormZone_${i}`;
            stormPart.Size = new Vector3(150, 1, 150);
            stormPart.Position = new Vector3(
                math.random(-400, 400),
                220 + math.random(-30, 30),
                math.random(-400, 400)
            );
            stormPart.Anchored = true;
            stormPart.CanCollide = false;
            stormPart.Transparency = 1;
            stormPart.Parent = Workspace;
            parts.push(stormPart);

            const stormEmitter = new Instance("ParticleEmitter");
            stormEmitter.Name = "StormEmitter";
            
            const config: WeatherConfig = {
                emissionRate: intensity * 200 + 100,
                lifetime: new NumberRange(1.5, 3),
                speed: new NumberRange(40 + intensity * 40, 80 + intensity * 60),
                acceleration: new Vector3(windSpeed, -80 - intensity * 30, windSpeed * 0.5),
                size: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.15),
                    new NumberSequenceKeypoint(0.3, 0.25),
                    new NumberSequenceKeypoint(1, 0.05)
                ]),
                transparency: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.2),
                    new NumberSequenceKeypoint(0.6, 0.3),
                    new NumberSequenceKeypoint(1, 1)
                ]),
                color: new ColorSequence([
                    new ColorSequenceKeypoint(0, Color3.fromRGB(180, 200, 230)),
                    new ColorSequenceKeypoint(1, Color3.fromRGB(120, 140, 180))
                ]),
                texture: "rbxasset://textures/particles/sparkles_main.dds",
                shape: Enum.ParticleEmitterShape.Box,
                shapeInOut: Enum.ParticleEmitterShapeInOut.Outward,
                shapeStyle: Enum.ParticleEmitterShapeStyle.Volume,
                windAffectsDrag: true,
                drag: 0.05,
                lightEmission: 0,
                lightInfluence: 1,
                orientation: Enum.ParticleOrientation.VelocityParallel,
                rate: intensity * 200 + 100,
                rotSpeed: new NumberRange(0, 0),
                rotation: new NumberRange(0, 0),
                spreadAngle: new Vector2(8, 8),
                velocityInheritance: 0,
                zOffset: 0
            };

            this.applyParticleConfig(stormEmitter, config);
            stormEmitter.Parent = stormPart;
            emitters.push(stormEmitter);
        }

        this.activeEmitters.set("storm", emitters);
        this.weatherParts.set("storm", parts);
        
        return emitters;
    }

    /**
     * Create thunderstorm effects with lightning and heavy rain
     */
    static createThunderstormEffect(intensity: number): ParticleEmitter[] {
        const emitters: ParticleEmitter[] = [];
        const parts: Part[] = [];

        const numZones = math.max(8, math.floor(intensity * 15));
        const windSpeed = intensity * 20 + 15;

        for (let i = 0; i < numZones; i++) {
            const thunderPart = new Instance("Part");
            thunderPart.Name = `ThunderstormZone_${i}`;
            thunderPart.Size = new Vector3(200, 1, 200);
            thunderPart.Position = new Vector3(
                math.random(-500, 500),
                250 + math.random(-40, 40),
                math.random(-500, 500)
            );
            thunderPart.Anchored = true;
            thunderPart.CanCollide = false;
            thunderPart.Transparency = 1;
            thunderPart.Parent = Workspace;
            parts.push(thunderPart);

            const thunderEmitter = new Instance("ParticleEmitter");
            thunderEmitter.Name = "ThunderstormEmitter";

            const config: WeatherConfig = {
                emissionRate: intensity * 250 + 150,
                lifetime: new NumberRange(1, 2.5),
                speed: new NumberRange(60 + intensity * 50, 100 + intensity * 80),
                acceleration: new Vector3(windSpeed * 1.2, -100 - intensity * 40, windSpeed * 0.8),
                size: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.2),
                    new NumberSequenceKeypoint(0.2, 0.3),
                    new NumberSequenceKeypoint(1, 0.05)
                ]),
                transparency: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.1),
                    new NumberSequenceKeypoint(0.5, 0.2),
                    new NumberSequenceKeypoint(1, 1)
                ]),
                color: new ColorSequence([
                    new ColorSequenceKeypoint(0, Color3.fromRGB(160, 180, 210)),
                    new ColorSequenceKeypoint(1, Color3.fromRGB(100, 120, 160))
                ]),
                texture: "rbxasset://textures/particles/sparkles_main.dds",
                shape: Enum.ParticleEmitterShape.Box,
                shapeInOut: Enum.ParticleEmitterShapeInOut.Outward,
                shapeStyle: Enum.ParticleEmitterShapeStyle.Volume,
                windAffectsDrag: true,
                drag: 0.02,
                lightEmission: 0.1,
                lightInfluence: 1,
                orientation: Enum.ParticleOrientation.VelocityParallel,
                rate: intensity * 250 + 150,
                rotSpeed: new NumberRange(0, 0),
                rotation: new NumberRange(0, 0),
                spreadAngle: new Vector2(10, 10),
                velocityInheritance: 0,
                zOffset: 0
            };

            this.applyParticleConfig(thunderEmitter, config);
            thunderEmitter.Parent = thunderPart;
            emitters.push(thunderEmitter);
        }

        this.activeEmitters.set("thunderstorm", emitters);
        this.weatherParts.set("thunderstorm", parts);

        return emitters;
    }

    /**
     * Create fog/mist effects
     */
    static createFogEffect(intensity: number): ParticleEmitter[] {
        const emitters: ParticleEmitter[] = [];
        const parts: Part[] = [];

        const numZones = math.max(5, math.floor(intensity * 10));

        for (let i = 0; i < numZones; i++) {
            const fogPart = new Instance("Part");
            fogPart.Name = `FogZone_${i}`;
            fogPart.Size = new Vector3(80, 1, 80);
            fogPart.Position = new Vector3(
                math.random(-200, 200),
                20 + math.random(-10, 30),
                math.random(-200, 200)
            );
            fogPart.Anchored = true;
            fogPart.CanCollide = false;
            fogPart.Transparency = 1;
            fogPart.Parent = Workspace;
            parts.push(fogPart);

            const fogEmitter = new Instance("ParticleEmitter");
            fogEmitter.Name = "FogEmitter";

            const config: WeatherConfig = {
                emissionRate: intensity * 40 + 20,
                lifetime: new NumberRange(8, 15),
                speed: new NumberRange(1, 3 + intensity * 2),
                acceleration: new Vector3(0, 2 + intensity, 0),
                size: new NumberSequence([
                    new NumberSequenceKeypoint(0, 2 + intensity * 3),
                    new NumberSequenceKeypoint(0.5, 4 + intensity * 5),
                    new NumberSequenceKeypoint(1, 6 + intensity * 7)
                ]),
                transparency: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.8),
                    new NumberSequenceKeypoint(0.3, 0.6 - intensity * 0.2),
                    new NumberSequenceKeypoint(1, 1)
                ]),
                color: new ColorSequence([
                    new ColorSequenceKeypoint(0, Color3.fromRGB(220, 220, 230)),
                    new ColorSequenceKeypoint(1, Color3.fromRGB(200, 200, 210))
                ]),
                texture: "rbxasset://textures/particles/sparkles_main.dds",
                shape: Enum.ParticleEmitterShape.Box,
                shapeInOut: Enum.ParticleEmitterShapeInOut.Outward,
                shapeStyle: Enum.ParticleEmitterShapeStyle.Volume,
                windAffectsDrag: true,
                drag: 0.8,
                lightEmission: 0,
                lightInfluence: 0.3,
                orientation: Enum.ParticleOrientation.FacingCamera,
                rate: intensity * 40 + 20,
                rotSpeed: new NumberRange(0, 0),
                rotation: new NumberRange(0, 0),
                spreadAngle: new Vector2(15, 15),
                velocityInheritance: 0,
                zOffset: 0
            };

            this.applyParticleConfig(fogEmitter, config);
            fogEmitter.Parent = fogPart;
            emitters.push(fogEmitter);
        }

        this.activeEmitters.set("fog", emitters);
        this.weatherParts.set("fog", parts);

        return emitters;
    }

    /**
     * Create sandstorm effects
     */
    static createSandstormEffect(intensity: number): ParticleEmitter[] {
        const emitters: ParticleEmitter[] = [];
        const parts: Part[] = [];

        const numZones = math.max(6, math.floor(intensity * 10));
        const windSpeed = intensity * 25 + 20;

        for (let i = 0; i < numZones; i++) {
            const sandPart = new Instance("Part");
            sandPart.Name = `SandstormZone_${i}`;
            sandPart.Size = new Vector3(120, 1, 120);
            sandPart.Position = new Vector3(
                math.random(-300, 300),
                50 + math.random(-20, 50),
                math.random(-300, 300)
            );
            sandPart.Anchored = true;
            sandPart.CanCollide = false;
            sandPart.Transparency = 1;
            sandPart.Parent = Workspace;
            parts.push(sandPart);

            const sandEmitter = new Instance("ParticleEmitter");
            sandEmitter.Name = "SandstormEmitter";

            const config: WeatherConfig = {
                emissionRate: intensity * 120 + 80,
                lifetime: new NumberRange(3, 6),
                speed: new NumberRange(15 + intensity * 20, 30 + intensity * 35),
                acceleration: new Vector3(windSpeed, -2 - intensity * 3, windSpeed * 0.6),
                size: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.3),
                    new NumberSequenceKeypoint(0.5, 0.5 + intensity * 0.4),
                    new NumberSequenceKeypoint(1, 0.2)
                ]),
                transparency: new NumberSequence([
                    new NumberSequenceKeypoint(0, 0.4),
                    new NumberSequenceKeypoint(0.6, 0.5 - intensity * 0.1),
                    new NumberSequenceKeypoint(1, 1)
                ]),
                color: new ColorSequence([
                    new ColorSequenceKeypoint(0, Color3.fromRGB(200, 170, 120)),
                    new ColorSequenceKeypoint(1, Color3.fromRGB(180, 150, 100))
                ]),
                texture: "rbxasset://textures/particles/sparkles_main.dds",
                shape: Enum.ParticleEmitterShape.Box,
                shapeInOut: Enum.ParticleEmitterShapeInOut.Outward,
                shapeStyle: Enum.ParticleEmitterShapeStyle.Volume,
                windAffectsDrag: true,
                drag: 0.2,
                lightEmission: 0,
                lightInfluence: 0.9,
                orientation: Enum.ParticleOrientation.FacingCamera,
                rate: intensity * 120 + 80,
                rotSpeed: new NumberRange(0, 0),
                rotation: new NumberRange(0, 0),
                spreadAngle: new Vector2(20, 20),
                velocityInheritance: 0,
                zOffset: 0
            };

            this.applyParticleConfig(sandEmitter, config);
            sandEmitter.Parent = sandPart;
            emitters.push(sandEmitter);
        }

        this.activeEmitters.set("sandstorm", emitters);
        this.weatherParts.set("sandstorm", parts);

        return emitters;
    }

    /**
     * Apply particle configuration to a ParticleEmitter
     */
    private static applyParticleConfig(emitter: ParticleEmitter, config: WeatherConfig): void {
        emitter.Rate = config.rate;
        emitter.Lifetime = config.lifetime;
        emitter.Speed = config.speed;
        emitter.Acceleration = config.acceleration;
        emitter.Size = config.size;
        emitter.Transparency = config.transparency;
        emitter.Color = config.color;
        emitter.Texture = config.texture;
        emitter.Shape = config.shape;
        emitter.ShapeInOut = config.shapeInOut;
        emitter.ShapeStyle = config.shapeStyle;
        emitter.WindAffectsDrag = config.windAffectsDrag;
        emitter.Drag = config.drag;
        emitter.LightEmission = config.lightEmission;
        emitter.LightInfluence = config.lightInfluence;
        emitter.Orientation = config.orientation;
        emitter.RotSpeed = config.rotSpeed;
        emitter.Rotation = config.rotation;
        emitter.SpreadAngle = config.spreadAngle;
        emitter.VelocityInheritance = config.velocityInheritance;
        emitter.ZOffset = config.zOffset;
        emitter.Enabled = true;
    }

    /**
     * Clear specific weather effects
     */
    static clearWeatherEffect(weatherType: string): void {
        print(`🧹 Clearing ${weatherType} weather effects`);

        // Clear emitters
        const emitters = this.activeEmitters.get(weatherType);
        if (emitters) {
            emitters.forEach(emitter => {
                if (emitter.Parent) {
                    emitter.Enabled = false;
                    task.delay(3, () => {
                        if (emitter.Parent) {
                            emitter.Destroy();
                        }
                    });
                }
            });
            this.activeEmitters.delete(weatherType);
        }

        // Clear parts
        const parts = this.weatherParts.get(weatherType);
        if (parts) {
            parts.forEach(part => {
                task.delay(3, () => {
                    if (part.Parent) {
                        part.Destroy();
                    }
                });
            });
            this.weatherParts.delete(weatherType);
        }

        // Clear rain connections
        if (weatherType === "rain") {
            this.rainConnections.forEach(connection => {
                if (connection.Connected) {
                    connection.Disconnect();
                }
            });
            this.rainConnections = [];
        }

        print(`✅ Cleared ${weatherType} weather effects`);
    }

    /**
     * Clear all active weather effects
     */
    static clearAllWeatherEffects(): void {
        // Clear all emitters
        this.activeEmitters.forEach((emitters) => {
            emitters.forEach(emitter => {
                if (emitter.Parent) {
                    emitter.Enabled = false;
                    task.delay(2, () => emitter.Destroy());
                }
            });
        });
        this.activeEmitters.clear();

        // Clear all parts
        this.weatherParts.forEach((parts) => {
            parts.forEach(part => {
                task.delay(2, () => part.Destroy());
            });
        });
        this.weatherParts.clear();

        print("🧹 Cleared all weather effects");
    }

    /**
     * Update weather intensity for active effects
     */
    static updateWeatherIntensity(weatherType: string, newIntensity: number): void {
        const emitters = this.activeEmitters.get(weatherType);
        if (!emitters) {
            print(`⚠️ No active ${weatherType} effects to update`);
            return;
        }

        // Update emission rate based on weather type and intensity
        emitters.forEach(emitter => {
            switch (weatherType) {
                case "rain":
                    emitter.Rate = newIntensity * 150 + 50;
                    break;
                case "snow":
                    emitter.Rate = newIntensity * 80 + 30;
                    break;
                case "storm":
                    emitter.Rate = newIntensity * 200 + 100;
                    break;
                case "thunderstorm":
                    emitter.Rate = newIntensity * 250 + 150;
                    break;
                case "fog":
                    emitter.Rate = newIntensity * 40 + 20;
                    break;
                case "sandstorm":
                    emitter.Rate = newIntensity * 120 + 80;
                    break;
            }
        });

        print(`🌦️ Updated ${weatherType} intensity to ${newIntensity}`);
    }

    /**
     * Get active weather types
     */
    static getActiveWeatherTypes(): string[] {
        const types: string[] = [];
        this.activeEmitters.forEach((_, key) => {
            types.push(key);
        });
        return types;
    }

    /**
     * Check if specific weather type is active
     */
    static isWeatherActive(weatherType: string): boolean {
        return this.activeEmitters.has(weatherType);
    }

    /**
     * Get number of active emitters for a weather type
     */
    static getEmitterCount(weatherType: string): number {
        const emitters = this.activeEmitters.get(weatherType);
        return emitters ? emitters.size() : 0;
    }

    /**
     * Create weather effect based on type and intensity
     */
    static createWeatherEffect(weatherType: string, intensity: number, windSpeed = 0): ParticleEmitter[] {
        print(`🌦️ Creating ${weatherType} weather with intensity ${intensity}`);

        // Clear existing effects of this type first
        this.clearWeatherEffect(weatherType);

        switch (weatherType) {
            case "rain":
            case "heavy_rain":
                return this.createRealisticRain(intensity, windSpeed);
            case "snow":
            case "blizzard":
                return this.createSnowEffect(intensity, windSpeed);
            case "storm":
                return this.createStormEffect(intensity);
            case "thunderstorm":
                return this.createThunderstormEffect(intensity);
            case "fog":
                return this.createFogEffect(intensity);
            case "sandstorm":
                return this.createSandstormEffect(intensity);
            default:
                print(`⚠️ Unknown weather type: ${weatherType}`);
                return [];
        }
    }

}

// Legacy alias for backward compatibility
export const WeatherParticleHelper = WeatherSystem;
