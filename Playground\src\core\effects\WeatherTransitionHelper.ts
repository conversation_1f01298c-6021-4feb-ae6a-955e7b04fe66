import { Lighting, TweenService } from "@rbxts/services";
import { WeatherType } from "../world/state/interfaces/WeatherOptions";

export interface LightingState {
    fogEnd: number;
    fogStart: number;
    fogColor: Color3;
    brightness: number;
    ambient: Color3;
}

export interface WeatherTransitionConfig {
    fromWeather: WeatherType;
    toWeather: WeatherType;
    duration: number;
    easingStyle?: Enum.EasingStyle;
    easingDirection?: Enum.EasingDirection;
    onComplete?: () => void;
    onUpdate?: (progress: number) => void;
}

export class WeatherTransitionHelper {
    private static activeTweens: Tween[] = [];
    private static isTransitioning = false;

    // Predefined lighting states for each weather type
    private static readonly LIGHTING_STATES: Map<WeatherType, LightingState> = new Map([
        ["clear", {
            fogEnd: 100000,
            fogStart: 0,
            fogColor: new Color3(0.76, 0.76, 0.76),
            brightness: 1,
            ambient: new Color3(0.5, 0.5, 0.5)
        }],
        ["rain", {
            fogEnd: 1500,
            fogStart: 0,
            fogColor: new Color3(0.6, 0.6, 0.6),
            brightness: 0.6,
            ambient: new Color3(0.3, 0.3, 0.4)
        }],
        ["heavy_rain", {
            fogEnd: 800,
            fogStart: 0,
            fogColor: new Color3(0.5, 0.5, 0.5),
            brightness: 0.4,
            ambient: new Color3(0.25, 0.25, 0.35)
        }],
        ["snow", {
            fogEnd: 1200,
            fogStart: 0,
            fogColor: new Color3(0.8, 0.8, 0.9),
            brightness: 0.7,
            ambient: new Color3(0.4, 0.4, 0.5)
        }],
        ["blizzard", {
            fogEnd: 300,
            fogStart: 0,
            fogColor: new Color3(0.8, 0.8, 0.9),
            brightness: 0.4,
            ambient: new Color3(0.4, 0.4, 0.5)
        }],
        ["storm", {
            fogEnd: 600,
            fogStart: 0,
            fogColor: new Color3(0.3, 0.3, 0.3),
            brightness: 0.3,
            ambient: new Color3(0.2, 0.2, 0.3)
        }],
        ["thunderstorm", {
            fogEnd: 400,
            fogStart: 0,
            fogColor: new Color3(0.2, 0.2, 0.2),
            brightness: 0.15,
            ambient: new Color3(0.1, 0.1, 0.2)
        }],
        ["fog", {
            fogEnd: 200,
            fogStart: 0,
            fogColor: new Color3(0.7, 0.7, 0.7),
            brightness: 0.4,
            ambient: new Color3(0.3, 0.3, 0.3)
        }],
        ["sandstorm", {
            fogEnd: 300,
            fogStart: 0,
            fogColor: new Color3(0.8, 0.7, 0.5),
            brightness: 0.5,
            ambient: new Color3(0.4, 0.3, 0.2)
        }]
    ]);

    /**
     * Transition smoothly between weather states
     */
    static transitionWeather(config: WeatherTransitionConfig): void {
        if (this.isTransitioning) {
            print("⚠️ Weather transition already in progress, stopping previous transition");
            this.stopCurrentTransition();
        }

        this.isTransitioning = true;

        const fromState = this.LIGHTING_STATES.get(config.fromWeather);
        const toState = this.LIGHTING_STATES.get(config.toWeather);

        if (!fromState || !toState) {
            print(`⚠️ Invalid weather states for transition: ${config.fromWeather} -> ${config.toWeather}`);
            this.isTransitioning = false;
            return;
        }

        print(`🌦️ Starting weather transition: ${config.fromWeather} -> ${config.toWeather} (${config.duration}s)`);

        // Create tween info
        const tweenInfo = new TweenInfo(
            config.duration,
            config.easingStyle || Enum.EasingStyle.Sine,
            config.easingDirection || Enum.EasingDirection.InOut
        );

        // Tween lighting properties
        const lightingTween = TweenService.Create(Lighting, tweenInfo, {
            FogEnd: toState.fogEnd,
            FogStart: toState.fogStart,
            FogColor: toState.fogColor,
            Brightness: toState.brightness,
            Ambient: toState.ambient
        });

        // Store the tween
        this.activeTweens.push(lightingTween);

        // Handle completion
        lightingTween.Completed.Connect(() => {
            this.isTransitioning = false;
            this.activeTweens = this.activeTweens.filter(tween => tween !== lightingTween);
            
            if (config.onComplete) {
                config.onComplete();
            }
            
            print(`✅ Weather transition completed: ${config.toWeather}`);
        });

        // Handle progress updates
        if (config.onUpdate) {
            const startTime = tick();
            const updateLoop = () => {
                if (!this.isTransitioning) return;
                
                const elapsed = tick() - startTime;
                const progress = math.min(elapsed / config.duration, 1);
                config.onUpdate!(progress);
                
                if (progress < 1) {
                    task.wait(0.1);
                    updateLoop();
                }
            };
            task.spawn(() => updateLoop());
        }

        // Start the transition
        lightingTween.Play();
    }

    /**
     * Get lighting state for a specific weather type with intensity adjustment
     */
    static getLightingStateForWeather(weatherType: WeatherType, intensity: number): LightingState {
        const baseState = this.LIGHTING_STATES.get(weatherType);
        if (!baseState) {
            return this.LIGHTING_STATES.get("clear")!;
        }

        // Adjust lighting based on intensity
        return {
            fogEnd: math.max(50, baseState.fogEnd - (intensity * baseState.fogEnd * 0.3)),
            fogStart: baseState.fogStart,
            fogColor: baseState.fogColor,
            brightness: math.max(0.05, baseState.brightness - (intensity * 0.2)),
            ambient: new Color3(
                math.max(0.05, baseState.ambient.R - (intensity * 0.1)),
                math.max(0.05, baseState.ambient.G - (intensity * 0.1)),
                math.max(0.05, baseState.ambient.B - (intensity * 0.1))
            )
        };
    }

    /**
     * Apply lighting state immediately (no transition)
     */
    static applyLightingState(state: LightingState): void {
        Lighting.FogEnd = state.fogEnd;
        Lighting.FogStart = state.fogStart;
        Lighting.FogColor = state.fogColor;
        Lighting.Brightness = state.brightness;
        Lighting.Ambient = state.ambient;
    }

    /**
     * Get current lighting state
     */
    static getCurrentLightingState(): LightingState {
        return {
            fogEnd: Lighting.FogEnd,
            fogStart: Lighting.FogStart,
            fogColor: Lighting.FogColor,
            brightness: Lighting.Brightness,
            ambient: Lighting.Ambient
        };
    }

    /**
     * Stop current weather transition
     */
    static stopCurrentTransition(): void {
        this.activeTweens.forEach(tween => {
            tween.Cancel();
        });
        this.activeTweens = [];
        this.isTransitioning = false;
        print("🛑 Weather transition stopped");
    }

    /**
     * Check if weather transition is in progress
     */
    static isTransitionInProgress(): boolean {
        return this.isTransitioning;
    }

    /**
     * Create a gradual weather intensity transition
     */
    static transitionWeatherIntensity(
        weatherType: WeatherType,
        fromIntensity: number,
        toIntensity: number,
        duration: number,
        onUpdate?: (intensity: number) => void,
        onComplete?: () => void
    ): void {
        const startTime = tick();
        const intensityDiff = toIntensity - fromIntensity;

        const intensityLoop = () => {
            const elapsed = tick() - startTime;
            const progress = math.min(elapsed / duration, 1);
            const currentIntensity = fromIntensity + (intensityDiff * progress);

            if (onUpdate) {
                onUpdate(currentIntensity);
            }

            if (progress >= 1) {
                if (onComplete) {
                    onComplete();
                }
            } else {
                task.wait(0.1);
                intensityLoop();
            }
        };

        task.spawn(() => intensityLoop());
    }

    /**
     * Create a smooth fade transition for weather effects
     */
    static fadeWeatherEffect(
        effectType: "in" | "out",
        duration: number,
        onUpdate?: (alpha: number) => void,
        onComplete?: () => void
    ): void {
        const startTime = tick();
        const startAlpha = effectType === "in" ? 0 : 1;
        const endAlpha = effectType === "in" ? 1 : 0;
        const alphaDiff = endAlpha - startAlpha;

        const fadeLoop = () => {
            const elapsed = tick() - startTime;
            const progress = math.min(elapsed / duration, 1);
            const currentAlpha = startAlpha + (alphaDiff * progress);

            if (onUpdate) {
                onUpdate(currentAlpha);
            }

            if (progress >= 1) {
                if (onComplete) {
                    onComplete();
                }
            } else {
                task.wait(0.05);
                fadeLoop();
            }
        };

        task.spawn(() => fadeLoop());
    }
}
