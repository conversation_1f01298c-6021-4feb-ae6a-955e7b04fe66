import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";

interface SliderProps {
  value: number; // Current value (0-1)
  onChange: (value: number) => void;
  min?: number; // Minimum value (default: 0)
  max?: number; // Maximum value (default: 1)
  step?: number; // Step size (default: 0.01)
  size?: UDim2;
  position?: UDim2;
  layoutOrder?: number;
  disabled?: boolean;
  label?: string;
  showValue?: boolean;
}

export function Slider(props: SliderProps): React.ReactElement {
  const [isDragging, setIsDragging] = React.useState(false);
  const [hovered, setHovered] = React.useState(false);
  const sliderRef = React.useRef<Frame>();

  const min = props.min ?? 0;
  const max = props.max ?? 1;
  const step = props.step ?? 0.01;
  const size = props.size ?? new UDim2(1, 0, 0, 30);

  // Calculate thumb position based on value
  const normalizedValue = (props.value - min) / (max - min);
  const thumbPosition = new UDim2(normalizedValue, -8, 0.5, -8);

  const updateValue = (inputObject: InputObject) => {
    if (!sliderRef.current || props.disabled) return;

    const frame = sliderRef.current;
    const relativeX = inputObject.Position.X - frame.AbsolutePosition.X;
    const percentage = math.clamp(relativeX / frame.AbsoluteSize.X, 0, 1);
    
    let newValue = min + (percentage * (max - min));
    
    // Apply step
    if (step > 0) {
      newValue = math.round(newValue / step) * step;
    }
    
    newValue = math.clamp(newValue, min, max);
    props.onChange(newValue);
  };

  const handleInputBegan = (rbx: Frame, inputObject: InputObject) => {
    if (props.disabled) return;

    if (inputObject.UserInputType === Enum.UserInputType.MouseButton1 ||
        inputObject.UserInputType === Enum.UserInputType.Touch) {
      setIsDragging(true);
      updateValue(inputObject);
    }
  };

  const handleInputChanged = (rbx: Frame, inputObject: InputObject) => {
    if (!isDragging || props.disabled) return;

    if (inputObject.UserInputType === Enum.UserInputType.MouseMovement ||
        inputObject.UserInputType === Enum.UserInputType.Touch) {
      updateValue(inputObject);
    }
  };

  const handleInputEnded = (rbx: Frame, inputObject: InputObject) => {
    if (inputObject.UserInputType === Enum.UserInputType.MouseButton1 ||
        inputObject.UserInputType === Enum.UserInputType.Touch) {
      setIsDragging(false);
    }
  };

  const trackColor = props.disabled ? COLORS.border.l1 : COLORS.bg.surface;
  const fillColor = props.disabled ? COLORS.border.l2 : COLORS.primary;
  const thumbColor = props.disabled ? COLORS.border.l2 :
                     (isDragging ? COLORS["primary-dark"] :
                      (hovered ? COLORS.primary : COLORS["primary-dark"]));

  return (
    <frame
      Size={size}
      Position={props.position}
      LayoutOrder={props.layoutOrder}
      BackgroundTransparency={1}
    >
      {/* Label */}
      {props.label && (
        <textlabel
          Text={props.label}
          Size={new UDim2(1, 0, 0, 16)}
          Position={new UDim2(0, 0, 0, -20)}
          BackgroundTransparency={1}
          TextColor3={Color3.fromHex(COLORS.text.main)}
          TextSize={SIZES.fontSize - 2}
          TextXAlignment={Enum.TextXAlignment.Left}
          Font={Enum.Font.SourceSans}
        />
      )}

      {/* Slider Track */}
      <frame
        ref={sliderRef}
        Size={new UDim2(1, 0, 0, 6)}
        Position={new UDim2(0, 0, 0.5, -3)}
        BackgroundColor3={Color3.fromHex(trackColor)}
        BorderSizePixel={0}
        Event={{
          InputBegan: handleInputBegan,
          InputChanged: handleInputChanged,
          InputEnded: handleInputEnded,
          MouseEnter: () => !props.disabled && setHovered(true),
          MouseLeave: () => setHovered(false),
        }}
      >
        <uicorner CornerRadius={new UDim(0, 3)} />

        {/* Fill */}
        <frame
          Size={new UDim2(normalizedValue, 0, 1, 0)}
          BackgroundColor3={Color3.fromHex(fillColor)}
          BorderSizePixel={0}
        >
          <uicorner CornerRadius={new UDim(0, 3)} />
        </frame>

        {/* Thumb */}
        <frame
          Size={new UDim2(0, 16, 0, 16)}
          Position={thumbPosition}
          BackgroundColor3={Color3.fromHex(thumbColor)}
          BorderSizePixel={0}
        >
          <uicorner CornerRadius={new UDim(0, 8)} />
          
          {/* Thumb border */}
          <uistroke
            Color={Color3.fromHex(COLORS.border.l1)}
            Thickness={1}
            Transparency={props.disabled ? 0.5 : 0}
          />
        </frame>
      </frame>

      {/* Value display */}
      {props.showValue && (
        <textlabel
          Text={tostring(math.round(props.value * 100) / 100)}
          Size={new UDim2(0, 40, 0, 16)}
          Position={new UDim2(1, 5, 0.5, -8)}
          BackgroundTransparency={1}
          TextColor3={Color3.fromHex(COLORS.text.secondary)}
          TextSize={SIZES.fontSize - 2}
          TextXAlignment={Enum.TextXAlignment.Left}
          Font={Enum.Font.SourceSans}
        />
      )}
    </frame>
  );
}
