import { Lighting, Workspace, TweenService } from "@rbxts/services";
import { WeatherOptions, WeatherType } from "./interfaces/WeatherOptions";
import { AtmosphereOptions } from "./interfaces/AtmosphereOptions";
import { WeatherSystem } from "../../effects/WeatherParticleHelper";
import { WeatherSoundSystem } from "../../effects/WeatherSoundHelper";
import { WeatherTransitionHelper } from "../../effects/WeatherTransitionHelper";

/**
 * WeatherController - Advanced weather system with realistic effects
 * Provides weather effects with proper particles, sounds, and transitions
 */
export class WeatherController {
    private static instance: WeatherController;
    private currentWeather: WeatherType = "clear";
    private currentIntensity = 0.5;
    private isTransitioning = false;

    private constructor() {
        print("🌦️ Weather Controller initialized");
    }

    public static getInstance(): WeatherController {
        if (!WeatherController.instance) {
            WeatherController.instance = new WeatherController();
        }
        return WeatherController.instance;
    }

    /**
     * Set weather with optional smooth transition
     */
    public setWeather(options: WeatherOptions): void {
        if (this.isTransitioning) {
            print("⚠️ Weather transition already in progress, skipping");
            return;
        }

        const previousWeather = this.currentWeather;
        const transitionDuration = options.transitionDuration || 0; // Default no transition for immediate effect

        print(`🌦️ Setting weather: ${previousWeather} -> ${options.type}`);

        // For now, always apply immediately for better testing
        this.applyWeatherImmediate(options);
    }

    /**
     * Apply weather change immediately without transition
     */
    private applyWeatherImmediate(options: WeatherOptions): void {
        print(`🌦️ Applying weather: ${options.type}`);

        this.currentWeather = options.type;

        // Clear existing weather effects
        this.clearWeatherEffects();

        switch (options.type) {
            case "clear":
                this.setClearWeather();
                break;
            case "rain":
                this.setRainWeather(options.intensity || 0.5);
                break;
            case "heavy_rain":
                this.setRainWeather(options.intensity || 0.8);
                break;
            case "snow":
                this.setSnowWeather(options.intensity || 0.5);
                break;
            case "blizzard":
                this.setSnowWeather(options.intensity || 0.9);
                break;
            case "storm":
                this.setStormWeather(options.intensity || 0.7);
                break;
            case "thunderstorm":
                this.setThunderstormWeather(options.intensity || 0.8);
                break;
            case "fog":
                this.setFogWeather(options.intensity || 0.6);
                break;
            case "sandstorm":
                this.setSandstormWeather(options.intensity || 0.7);
                break;
        }

        print(`✅ Weather changed to: ${options.type}`);
    }

    private setClearWeather(): void {
        print("☀️ Setting clear weather");

        // Clear, bright day lighting
        Lighting.FogEnd = 100000;
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.76, 0.76, 0.76);
        Lighting.Brightness = 1;
        Lighting.Ambient = new Color3(0.5, 0.5, 0.5);

        // Stop all weather effects
        WeatherSystem.clearAllWeatherEffects();
        WeatherSoundSystem.stopAllWeatherSounds();

        this.currentIntensity = 0;
        print("✅ Clear weather set");
    }

    private setRainWeather(intensity: number): void {
        print(`🌧️ Setting HIGHLY VISIBLE rain weather with intensity ${intensity}`);

        // Overcast and rainy lighting
        Lighting.FogEnd = math.max(500, 2000 - (intensity * 1500));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.6, 0.6, 0.6);
        Lighting.Brightness = math.max(0.3, 0.8 - (intensity * 0.4));
        Lighting.Ambient = new Color3(0.3, 0.3, 0.4);

        // Create MASSIVE, HIGHLY VISIBLE rain that falls to the ground
        // - 1000+ particles per emitter (vs 300 before)
        // - 9 total emitters (vs 4 before)
        // - Much larger particle sizes (0.8-1.7 vs 0.15-0.25)
        // - Completely opaque particles (0.0 transparency vs 0.1-0.7)
        // - Bright white color for maximum visibility
        // - Huge coverage areas (1000x1000 vs 500x500)
        const windSpeed = intensity * 5;
        WeatherSystem.createRealisticRain(intensity, windSpeed);

        // Play layered rain ambient sounds with atmospheric effects
        WeatherSoundSystem.playLayeredWeatherAmbient("rain", intensity);

        this.currentIntensity = intensity;
        print(`✅ MASSIVE VISIBLE rain weather set with intensity ${intensity}`);
    }

    // Placeholder methods for other weather types - using rain as base
    private setSnowWeather(intensity: number): void {
        print(`❄️ Setting snow weather with intensity ${intensity}`);
        this.setRainWeather(intensity); // Use rain system for now
    }

    private setStormWeather(intensity: number): void {
        print(`🌪️ Setting storm weather with intensity ${intensity}`);
        this.setRainWeather(intensity); // Use rain system for now
    }

    private setThunderstormWeather(intensity: number): void {
        print(`⛈️ Setting thunderstorm weather with intensity ${intensity}`);
        this.setRainWeather(intensity); // Use rain system for now
    }

    private setFogWeather(intensity: number): void {
        print(`🌫️ Setting fog weather with intensity ${intensity}`);
        this.setRainWeather(intensity); // Use rain system for now
    }

    private setSandstormWeather(intensity: number): void {
        print(`🏜️ Setting sandstorm weather with intensity ${intensity}`);
        this.setRainWeather(intensity); // Use rain system for now
    }

    /**
     * Get current weather type
     */
    public getCurrentWeather(): WeatherType {
        return this.currentWeather;
    }

    /**
     * Get current weather intensity
     */
    public getCurrentIntensity(): number {
        return this.currentIntensity;
    }

    /**
     * Clear weather effects
     */
    private clearWeatherEffects(): void {
        print("🧹 Clearing weather effects");
        WeatherSystem.clearAllWeatherEffects();
        WeatherSoundSystem.stopAllWeatherSounds();
    }

    /**
     * Cleanup all weather effects
     */
    public cleanup(): void {
        print("🧹 Cleaning up weather controller");
        this.clearWeatherEffects();
        this.setClearWeather();
    }
}